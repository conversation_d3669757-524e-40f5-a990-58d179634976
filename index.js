const express = require('express');
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const bodyParser = require('body-parser');

const app = express();
const PORT = 3000;

app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

// Storage untuk multiple sessions
const sessions = new Map();

class WhatsAppSession {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.status = 'initializing';
        this.latestQR = null;
        this.client = null;
        this.isRestored = false;
        this.isDestroying = false;
        this.init();
    }

    // Perbaiki destroy method untuk benar-benar tutup browser
    async destroy() {
        if (this.isDestroying) {
            console.log(`⚠️ Session ${this.sessionId} already being destroyed`);
            return;
        }
        
        this.isDestroying = true;
        this.status = 'disconnected';
        
        console.log(`🗑️ Destroying session: ${this.sessionId}`);
        
        // Destroy client dengan force
        if (this.client) {
            try {
                // Remove listeners first
                this.client.removeAllListeners();
                
                // Force destroy dengan timeout
                await Promise.race([
                    this.client.destroy(),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Destroy timeout')), 5000))
                ]);
                
                console.log(`✅ Client destroyed: ${this.sessionId}`);
            } catch (destroyError) {
                console.log(`⚠️ Force destroy: ${destroyError.message}`);
            }
            
            this.client = null;
        }
        
        // Wait longer for browser to fully close
        await new Promise(resolve => setTimeout(resolve, 2000));
    }


    init() {
        // Cek apakah session sudah ada di folder
        const sessionPath = path.join(__dirname, '.wwebjs_auth', `session-${this.sessionId}`);
        this.isRestored = fs.existsSync(sessionPath);
        
        if (this.isRestored) {
            console.log(`🔄 Restoring existing session: ${this.sessionId}`);
            this.status = 'restoring';
        }

        // Inisialisasi WhatsApp client dengan session terpisah
        this.client = new Client({
            authStrategy: new LocalAuth({ clientId: this.sessionId }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            }
        });

        this.setupEventListeners();
        this.client.initialize();
    }

    // Tambahkan script debug ini di dalam class WhatsAppSession

// Di dalam method setupEventListeners(), tambahkan lebih banyak log:
setupEventListeners() {
    console.log(`🔧 Setting up event listeners untuk session ${this.sessionId}`);
    
    // Wrap semua event handlers dengan try-catch
    this.client.on('error', (error) => {
        if (this.isDestroying) return;
        try {
            console.error(`💥 Client error untuk session ${this.sessionId}:`, error.message);
            this.status = 'disconnected';
        } catch (e) {
            console.log('⚠️ Error in error handler (ignored)');
        }
    });

    this.client.on('disconnected', (reason) => {
        if (this.isDestroying) return;
        try {
            console.log(`❌ WhatsApp Client disconnected untuk session ${this.sessionId}:`, reason);
            this.status = 'disconnected';
        } catch (e) {
            console.log('⚠️ Error in disconnected handler (ignored)');
        }
    });

    this.client.on('qr', (qr) => {
        if (this.isDestroying) return;
        try {
            console.log(`📱 QR Event triggered untuk session ${this.sessionId}`);
            this.latestQR = qr;
            this.status = 'qr_ready';
        } catch (e) {
            console.log('⚠️ Error in QR handler (ignored)');
        }
    });

    this.client.on('authenticated', () => {
        if (this.isDestroying) return;
        try {
            console.log(`✅ WhatsApp Client authenticated untuk session ${this.sessionId}!`);
            this.status = 'authenticated';
            this.latestQR = null;
        } catch (e) {
            console.log('⚠️ Error in authenticated handler (ignored)');
        }
    });

    this.client.on('ready', () => {
        if (this.isDestroying) return;
        try {
            console.log(`✅ WhatsApp Client ready untuk session ${this.sessionId}!`);
            this.status = 'ready';
            this.latestQR = null;
        } catch (e) {
            console.log('⚠️ Error in ready handler (ignored)');
        }
    });


}

    getDebugInfo() {
        return {
            sessionId: this.sessionId,
            status: this.status,
            hasQR: this.latestQR !== null,
            clientReady: this.client && this.client.info ? true : false,
            timestamp: new Date().toISOString()
        };
    }

    async sendMessage(number, message) {
        if (this.status !== 'ready') {
            throw new Error('Session not ready');
        }

        // Format nomor
        let formattedNumber = number.replace(/\D/g, '');
        
        if (formattedNumber.startsWith('0')) {
            formattedNumber = '62' + formattedNumber.substring(1);
        }
        
        if (!formattedNumber.startsWith('62')) {
            formattedNumber = '62' + formattedNumber;
        }

        const chatId = formattedNumber + "@c.us";
        
        // Verifikasi nomor terdaftar di WhatsApp
        const isRegistered = await this.client.isRegisteredUser(chatId);
        if (!isRegistered) {
            throw new Error(`Nomor ${formattedNumber} tidak terdaftar di WhatsApp`);
        }

        await this.client.sendMessage(chatId, message);
        return { success: true, number: formattedNumber, message };
    }

    async logout() {
        try {
            console.log(`🔒 Logout session: ${this.sessionId}`);
            
            // Set status disconnected dulu
            this.status = 'disconnected';
            
            // Destroy client dengan timeout protection
            if (this.client) {
                try {
                    await Promise.race([
                        this.client.destroy(),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Destroy timeout')), 10000))
                    ]);
                    console.log(`✅ Client destroyed untuk logout: ${this.sessionId}`);
                } catch (destroyError) {
                    console.log(`⚠️ Force destroy during logout: ${destroyError.message}`);
                }
                
                // Wait for cleanup
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
            
            // Hapus folder session dengan retry
            const sessionPath = path.join(__dirname, '.wwebjs_auth', `session-${this.sessionId}`);
            if (fs.existsSync(sessionPath)) {
                try {
                    await deleteSessionFolder(sessionPath);
                    console.log(`✅ Session folder deleted: ${sessionPath}`);
                } catch (error) {
                    console.error(`⚠️ Error menghapus folder saat logout: ${error.message}`);
                }
            }

            // Reset status
            this.status = 'initializing';
            this.latestQR = null;
            this.client = null;

            // Reinitialize
            this.init();
            
            return { success: true };
        } catch (error) {
            console.error(`❌ Logout error untuk session ${this.sessionId}:`, error);
            // Jangan throw error, return success anyway
            return { success: true, warning: error.message };
        }
    }
}

// Function untuk mendapatkan atau membuat session dengan sanitize
function getOrCreateSession(sessionId) {
    // Sanitize sessionId untuk memastikan valid
    const cleanSessionId = sanitizeSessionId(sessionId);
    
    if (!cleanSessionId) {
        throw new Error('Invalid session ID after sanitization');
    }
    
    if (!sessions.has(cleanSessionId)) {
        sessions.set(cleanSessionId, new WhatsAppSession(cleanSessionId));
    }
    return sessions.get(cleanSessionId);
}

// Fungsi untuk restore sessions yang sudah ada
function restoreExistingSessions() {
    const authPath = path.join(__dirname, '.wwebjs_auth');
    
    console.log(`🔍 Checking auth path: ${authPath}`);
    
    if (!fs.existsSync(authPath)) {
        console.log('📁 Folder .wwebjs_auth tidak ditemukan, skip restore');
        return;
    }
    
    try {
        const folders = fs.readdirSync(authPath);
        console.log(`📁 Found folders in .wwebjs_auth:`, folders);
        
        let restoredCount = 0;
        
        folders.forEach(folder => {
            console.log(`🔍 Processing folder: ${folder}`);
            
            // Cek apakah folder adalah session (format: session-{sessionId})
            if (folder.startsWith('session-')) {
                const sessionId = folder.replace('session-', '');
                const folderPath = path.join(authPath, folder);
                const markerPath = path.join(folderPath, '.DELETE_MARKER');
                
                // Skip jika ada delete marker
                if (fs.existsSync(markerPath)) {
                    console.log(`🗑️ Skipping marked for deletion: ${sessionId}`);
                    
                    // Try to delete the folder now
                    setTimeout(async () => {
                        try {
                            console.log(`🗑️ Attempting delayed delete: ${folderPath}`);
                            await deleteSessionFolder(folderPath);
                            console.log(`✅ Delayed delete successful: ${sessionId}`);
                        } catch (delayedError) {
                            console.log(`⚠️ Delayed delete failed: ${delayedError.message}`);
                        }
                    }, 5000); // 5 second delay
                    
                    return;
                }
                
                console.log(`📱 Found session folder for: ${sessionId}`);
                
                // Validasi sessionId
                const idRegex = /^[-_\w]+$/i;
                if (idRegex.test(sessionId)) {
                    // Cek apakah session sudah ada di memory
                    if (!sessions.has(sessionId)) {
                        console.log(`🔄 Restoring session: ${sessionId}`);
                        
                        try {
                            // Buat session baru (akan otomatis load dari folder yang ada)
                            const session = new WhatsAppSession(sessionId);
                            sessions.set(sessionId, session);
                            restoredCount++;
                            
                            console.log(`✅ Session ${sessionId} restored`);
                        } catch (restoreError) {
                            console.error(`❌ Failed to restore session ${sessionId}:`, restoreError.message);
                            // Hapus folder yang error
                            console.log(`🗑️ Attempting to remove corrupted folder: ${folderPath}`);
                            try {
                                fs.rmSync(folderPath, { recursive: true, force: true });
                                console.log(`🗑️ Removed corrupted session folder: ${sessionId}`);
                            } catch (cleanupError) {
                                console.error(`⚠️ Failed to cleanup corrupted folder: ${cleanupError.message}`);
                            }
                        }
                    } else {
                        console.log(`⚠️ Session ${sessionId} already exists in memory, skipping restore`);
                    }
                } else {
                    console.log(`⚠️ Invalid session ID format: ${sessionId}`);
                }
            } else {
                console.log(`📁 Skipping non-session folder: ${folder}`);
            }
        });
        
        console.log(`🎉 Restored ${restoredCount} sessions dari folder`);
        
    } catch (error) {
        console.error('❌ Error restoring sessions:', error);
    }
}

// API: Debug Session Info
app.get('/api/:sessionId/debug', (req, res) => {
    const sessionId = req.params.sessionId;
    
    if (!sessions.has(sessionId)) {
        return res.json({
            success: false,
            error: 'Session not found',
            sessionId
        });
    }
    
    const session = sessions.get(sessionId);
    
    res.json({
        success: true,
        debug: session.getDebugInfo(),
        timestamp: new Date().toISOString()
    });
});

// Tambahkan force restart session
app.post('/api/:sessionId/restart', async (req, res) => {
    const sessionId = req.params.sessionId;
    
    try {
        if (sessions.has(sessionId)) {
            const session = sessions.get(sessionId);
            await session.client.destroy();
            sessions.delete(sessionId);
        }
        
        // Buat session baru
        const newSession = getOrCreateSession(sessionId);
        
        res.json({
            success: true,
            message: 'Session restarted successfully',
            sessionId,
            status: newSession.status
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            sessionId
        });
    }
});

// Update function getStatusConfig untuk menambah debugging info
function getStatusConfig(status) {
    const configs = {
        initializing: {
            color: '#f59e0b',
            icon: '🔄',
            text: 'Menginisialisasi...',
            description: 'Sedang memulai WhatsApp client. Jika terlalu lama, coba restart session.',
            bg: '#fef3c7',
            textColor: '#92400e',
            descColor: '#d97706'
        },
        restoring: {
            color: '#8b5cf6',
            icon: '🔄',
            text: 'Memulihkan Session...',
            description: 'Sedang memulihkan session yang sudah ada',
            bg: '#ede9fe',
            textColor: '#5b21b6',
            descColor: '#7c3aed'
        },
        qr_ready: {
            color: '#3b82f6',
            icon: '📱',
            text: 'Siap untuk Scan',
            description: 'Scan QR code dengan WhatsApp di HP Anda',
            bg: '#dbeafe',
            textColor: '#1e40af',
            descColor: '#2563eb'
        },
        authenticated: {
            color: '#10b981',
            icon: '✅',
            text: 'Terotentikasi',
            description: 'Berhasil login, sedang menunggu siap...',
            bg: '#d1fae5',
            textColor: '#065f46',
            descColor: '#059669'
        },
        ready: {
            color: '#10b981',
            icon: '🚀',
            text: 'Siap Digunakan',
            description: 'Gateway siap mengirim dan menerima pesan',
            bg: '#d1fae5',
            textColor: '#065f46',
            descColor: '#059669'
        },
        disconnected: {
            color: '#ef4444',
            icon: '❌',
            text: 'Terputus',
            description: 'Koneksi terputus, silakan logout dan login ulang',
            bg: '#fee2e2',
            textColor: '#991b1b',
            descColor: '#dc2626'
        },
        auth_failed: {
            color: '#ef4444',
            icon: '⚠️',
            text: 'Autentikasi Gagal',
            description: 'Login gagal, silakan logout dan coba lagi',
            bg: '#fee2e2',
            textColor: '#991b1b',
            descColor: '#dc2626'
        }
    };
    
    return configs[status] || configs.initializing;
}

// Dashboard utama - list semua sessions
app.get('/', (req, res) => {
    // Handle redirect untuk create session
    if (req.query.create) {
        const sessionId = req.query.create.replace(/[^a-zA-Z0-9_-]/g, '');
        if (sessionId) {
            return res.redirect(`/session/${sessionId}`);
        }
    }

    const sessionList = Array.from(sessions.keys());
    
    let sessionsHTML = '';
    if (sessionList.length > 0) {
        sessionList.forEach(sessionId => {
            const session = sessions.get(sessionId);
            const statusConfig = getStatusConfig(session.status);
            
            sessionsHTML += `
                <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); border: 1px solid #e5e7eb; margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h3 style="margin: 0 0 8px 0; color: #374151;">📱 ${sessionId}</h3>
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <span style="background: ${statusConfig.bg}; color: ${statusConfig.textColor}; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">
                                    ${statusConfig.icon} ${statusConfig.text}
                                </span>
                            </div>
                            <p style="margin: 0; color: ${statusConfig.descColor}; font-size: 14px;">${statusConfig.description}</p>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <a href="/session/${sessionId}" style="background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 6px; font-size: 14px;">🔧 Kelola</a>
                            
                            <button onclick="deleteSession('${sessionId}')" style="background: #ef4444; color: white; padding: 8px 16px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;">🗑️ Hapus</button>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        sessionsHTML = `
            <div style="text-align: center; padding: 48px; background: #f8fafc; border-radius: 12px; border: 2px dashed #cbd5e1;">
                <div style="font-size: 64px; margin-bottom: 16px;">📱</div>
                <h3 style="color: #374151; margin: 0 0 8px 0;">Belum Ada Session</h3>
                <p style="color: #6b7280; margin: 0;">Buat session baru untuk memulai</p>
            </div>
        `;
    }

    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Multi-Session WhatsApp Gateway</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; background: #f9fafb; }
                .container { max-width: 1200px; margin: 0 auto; padding: 24px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div style="text-align: center; margin-bottom: 32px;">
                    <h1 style="color: #111827; margin-bottom: 8px;">📱 Multi-Session WhatsApp Gateway</h1>
                    <p style="color: #6b7280; margin: 0;">Kelola multiple session WhatsApp dalam satu dashboard</p>
                </div>

                <div style="background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); border: 1px solid #e5e7eb; margin-bottom: 24px;">
                    <h2 style="color: #374151; margin-top: 0;">➕ Buat Session Baru</h2>
                    <form onsubmit="createSession(event)" style="display: flex; gap: 12px; align-items: end;">
                        <div style="flex: 1;">
                            <label style="display: block; margin-bottom: 4px; color: #374151; font-weight: 500;">Session ID:</label>
                            <input type="text" id="sessionId" placeholder="contoh: project1, client-abc, dll" 
                                   style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" required>
                        </div>
                        <button type="submit" style="background: #10b981; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-weight: 500; cursor: pointer;">
                            🚀 Buat Session
                        </button>
                    </form>
                </div>

                <div>
                    <h2 style="color: #374151; margin-bottom: 16px;">📋 Daftar Sessions (${sessionList.length})</h2>
                    ${sessionsHTML}
                </div>
            </div>

            <script>
                function createSession(event) {
                    event.preventDefault();
                    const sessionId = document.getElementById('sessionId').value.trim();
                    if (sessionId) {
                        window.location.href = '/session/' + encodeURIComponent(sessionId);
                    }
                }

                function deleteSession(sessionId) {
                    console.log('Delete session clicked:', sessionId);
                    
                    if (confirm('⚠️ PERINGATAN!\\n\\nYakin ingin menghapus session "' + sessionId + '"?\\n\\n• Session akan dihentikan\\n• Data WhatsApp akan dihapus\\n• Perlu scan QR lagi untuk login\\n\\nTindakan ini tidak dapat dibatalkan!')) {
                        
                        // Tampilkan loading
                        const button = event.target;
                        const originalText = button.innerHTML;
                        button.innerHTML = '⏳ Menghapus...';
                        button.disabled = true;
                        
                        console.log('Sending DELETE request to:', '/api/' + sessionId + '/delete');
                        
                        fetch('/api/' + sessionId + '/delete', { 
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => {
                            console.log('Response status:', response.status);
                            if (!response.ok) {
                                throw new Error('HTTP ' + response.status);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Response data:', data);
                            if (data.success) {
                                alert('✅ Session "' + sessionId + '" berhasil dihapus!');
                                // Auto refresh halaman setelah 1 detik
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                alert('❌ Gagal menghapus session: ' + (data.error || 'Unknown error'));
                                button.innerHTML = originalText;
                                button.disabled = false;
                            }
                        })
                        .catch(error => {
                            console.error('Delete error:', error);
                            alert('❌ Error menghapus session: ' + error.message);
                            button.innerHTML = originalText;
                            button.disabled = false;
                        });
                    }
                }

                // Auto refresh setiap 30 detik untuk update status
                setInterval(() => {
                    window.location.reload();
                }, 30000);
            </script>
        </body>
        </html>
    `);
});

// Halaman management session individual
app.get('/session/:sessionId', async (req, res) => {
    const rawSessionId = req.params.sessionId;
    const sessionId = sanitizeSessionId(rawSessionId);
    
    if (!sessionId) {
        return res.status(400).send('Invalid session ID');
    }
    
    // Auto-create session jika belum ada
    const session = getOrCreateSession(sessionId);
    
    let qrSection = '';
    
    if (session.status === 'qr_ready' && session.latestQR) {
        const qrImage = await qrcode.toDataURL(session.latestQR);
        qrSection = `
            <div style="text-align: center; padding: 24px; background: #f8fafc; border-radius: 12px; border: 2px dashed #cbd5e1;">
                <h3 style="margin-top: 0; color: #374151;">Scan QR Code</h3>
                <img src="${qrImage}" style="max-width: 300px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);" />
                <p style="color: #6b7280; margin-bottom: 0; font-size: 14px;">
                    Buka WhatsApp di HP → Menu (3 titik) → Linked devices → Link a device
                </p>
            </div>
        `;
    } else {
        const statusConfig = getStatusConfig(session.status);
        qrSection = `
            <div style="text-align: center; padding: 24px; background: ${statusConfig.bg}; border-radius: 12px; border: 2px solid ${statusConfig.color};">
                <h3 style="margin-top: 0; color: ${statusConfig.textColor};">${statusConfig.icon} ${statusConfig.text}</h3>
                <p style="color: ${statusConfig.descColor}; margin-bottom: 0;">${statusConfig.description}</p>
            </div>
        `;
    }

    // Form kirim pesan
    const sendFormDisabled = session.status !== 'ready';
    


    // Auto refresh untuk QR dan status loading
    let refreshMeta = '';
    if (session.status === 'qr_ready' || session.status === 'initializing' || session.status === 'authenticated') {
        refreshMeta = '<meta http-equiv="refresh" content="10">';
    }

    res.send(`
        <!DOCTYPE html>
        <html>
            <head>
                <title>Session: ${sessionId} - WhatsApp Gateway</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                ${refreshMeta}
                <style>
                    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f9fafb; }
                    .container { max-width: 800px; margin: 0 auto; }
                    input, textarea, button { padding: 12px; border-radius: 6px; border: 1px solid #d1d5db; font-size: 14px; }
                    button { background: #3b82f6; color: white; border: none; cursor: pointer; }
                    button:disabled { background: #9ca3af; cursor: not-allowed; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div style="text-align: center; margin-bottom: 32px;">
                        <h1 style="color: #1f2937; margin: 0; font-size: 32px;">📱 ${sessionId}</h1>
                        <p style="color: #6b7280; margin: 8px 0 0 0;">WhatsApp Gateway Session</p>
                        <a href="/" style="display: inline-block; margin-top: 12px; color: #3b82f6; text-decoration: none; font-size: 14px;">← Kembali ke Dashboard</a>
                    </div>

                    <div style="margin-bottom: 24px;">
                        ${qrSection}
                    </div>

                    <!-- Send Message Form -->
                    <div style="background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); border: 1px solid #e5e7eb; margin-bottom: 24px; opacity: ${sendFormDisabled ? '0.5' : '1'};">
                        <h3 style="margin-top: 0; color: #374151;">📤 Kirim Pesan ${sendFormDisabled ? '(Tidak Aktif)' : ''}</h3>
                        <form method="POST" action="/session/${sessionId}/send">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">Nomor HP:</label>
                                <input type="text" name="number" ${sendFormDisabled ? 'disabled' : ''} style="width: 100%; box-sizing: border-box;" placeholder="628123456789" required>
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #374151;">Pesan:</label>
                                <textarea name="message" rows="4" ${sendFormDisabled ? 'disabled' : ''} style="width: 100%; box-sizing: border-box; resize: vertical;" placeholder="Tulis pesan..." required></textarea>
                            </div>
                            <button type="submit" ${sendFormDisabled ? 'disabled' : ''}>📤 Kirim Pesan</button>
                        </form>
                    </div>

                    <!-- Actions -->
                    <div style="text-align: center; margin-top: 24px;">
                        <button onclick="logout()" style="background: #ef4444; margin-right: 12px;">🔒 Logout</button>
                        <a href="/api/${sessionId}/status" target="_blank" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-size: 14px;">📡 Test API</a>
                    </div>

                    <script>
                        function logout() {
                            if (confirm("Yakin ingin logout session ${sessionId}?")) {
                                fetch('/session/${sessionId}/logout', { method: 'POST' })
                                    .then(res => res.json())
                                    .then(data => {
                                        alert(data.message || 'Logout berhasil');
                                        window.location.reload();
                                    })
                                    .catch(err => alert("Error logout: " + err));
                            }
                        }

                        // Auto refresh messages setiap 5 detik jika ready
                        if (${session.status === 'ready'}) {
                            setInterval(() => {
                                window.location.reload();
                            }, 30000);
                        }
                    </script>
                </div>
            </body>
        </html>
    `);
});

// Handle form kirim pesan dari web interface
app.post('/session/:sessionId/send', async (req, res) => {
    const sessionId = req.params.sessionId;
    const { number, message } = req.body;
    
    if (!sessions.has(sessionId)) {
        return res.redirect(`/session/${sessionId}?error=session_not_found`);
    }
    
    const session = sessions.get(sessionId);
    
    try {
        await session.sendMessage(number, message);
        res.redirect(`/session/${sessionId}?success=message_sent`);
    } catch (error) {
        console.error('Send message error:', error);
        res.redirect(`/session/${sessionId}?error=send_failed`);
    }
});

// Logout session
app.post('/session/:sessionId/logout', async (req, res) => {
    const sessionId = req.params.sessionId;
    
    if (!sessions.has(sessionId)) {
        return res.json({ success: false, message: 'Session tidak ditemukan' });
    }
    
    try {
        const session = sessions.get(sessionId);
        await session.logout();
        res.json({ success: true, message: 'Logout berhasil' });
    } catch (error) {
        res.json({ success: false, message: 'Logout gagal: ' + error.message });
    }
});

// ===================
// API ENDPOINTS
// ===================

// API: Send Message
app.post('/api/:sessionId/send', async (req, res) => {
    const rawSessionId = req.params.sessionId;
    const sessionId = sanitizeSessionId(rawSessionId);
    const { number, message } = req.body;
    
    if (!sessionId) {
        return res.status(400).json({
            success: false,
            error: 'Invalid session ID'
        });
    }
    
    if (!number || !message) {
        return res.status(400).json({
            success: false,
            error: 'Missing required fields: number, message'
        });
    }
    
    try {
        // Auto-create session jika belum ada
        const session = getOrCreateSession(sessionId);
        
        if (session.status !== 'ready') {
            return res.status(400).json({
                success: false,
                error: `Session not ready. Current status: ${session.status}`,
                status: session.status
            });
        }
        
        const result = await session.sendMessage(number, message);
        
        res.json({
            success: true,
            data: result,
            sessionId,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            sessionId,
            timestamp: new Date().toISOString()
        });
    }
});

// API: Get Session Status
app.get('/api/:sessionId/status', (req, res) => {
    const rawSessionId = req.params.sessionId;
    const sessionId = sanitizeSessionId(rawSessionId);
    
    if (!sessionId) {
        return res.status(400).json({
            success: false,
            error: 'Invalid session ID'
        });
    }
    
    const session = getOrCreateSession(sessionId);
    
    res.json({
        success: true,
        sessionId,
        status: session.status,
        ready: session.status === 'ready',
        hasQR: session.status === 'qr_ready' && session.latestQR !== null,
        timestamp: new Date().toISOString()
    });
});



// API: Get QR Code
app.get('/api/:sessionId/qr', async (req, res) => {
    const sessionId = req.params.sessionId;
    const session = getOrCreateSession(sessionId);
    
    if (session.status !== 'qr_ready' || !session.latestQR) {
        return res.status(400).json({
            success: false,
            error: 'QR code not available',
            status: session.status
        });
    }
    
    try {
        const qrImage = await qrcode.toDataURL(session.latestQR);
        res.json({
            success: true,
            sessionId,
            qrCode: session.latestQR,
            qrImage,
            status: session.status
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API: List All Sessions
app.get('/api/sessions', (req, res) => {
    const sessionList = [];
    
    sessions.forEach((session, sessionId) => {
        sessionList.push({
            sessionId,
            status: session.status,
            ready: session.status === 'ready',
            hasQR: session.status === 'qr_ready' && session.latestQR !== null
        });
    });
    
    res.json({
        success: true,
        sessions: sessionList,
        totalSessions: sessionList.length,
        timestamp: new Date().toISOString()
    });
});

// API: Delete Session - dengan sanitize
app.delete('/api/:sessionId/delete', async (req, res) => {
    const rawSessionId = req.params.sessionId;
    const sessionId = sanitizeSessionId(rawSessionId);
    
    if (!sessionId) {
        return res.status(400).json({
            success: false,
            error: 'Invalid session ID'
        });
    }
    
    console.log(`🗑️ DELETE request received for session: ${sessionId}`);
    
    try {
        let sessionFound = false;
        
        // Destroy session dengan proper cleanup
        if (sessions.has(sessionId)) {
            const session = sessions.get(sessionId);
            console.log(`📱 Found session in memory: ${sessionId}`);
            
            // Destroy session properly
            await session.destroy();
            
            // Remove dari Maps
            sessions.delete(sessionId);
            
            sessionFound = true;
            console.log(`✅ Session removed from memory: ${sessionId}`);
        }
        
        // Mark folder for deletion (delayed)
        const sessionPath = path.join(__dirname, '.wwebjs_auth', `session-${sessionId}`);
        
        if (fs.existsSync(sessionPath)) {
            console.log(`📁 Marking folder for deletion: ${sessionPath}`);
            
            // Create a marker file to indicate this session should be deleted
            const markerPath = path.join(sessionPath, '.DELETE_MARKER');
            try {
                fs.writeFileSync(markerPath, new Date().toISOString());
                console.log(`✅ Delete marker created: ${markerPath}`);
            } catch (markerError) {
                console.log(`⚠️ Could not create delete marker: ${markerError.message}`);
            }
        }
        
        console.log(`✅ Delete operation completed for: ${sessionId}`);
        
        // Always return success
        res.json({
            success: true,
            message: `Session ${sessionId} berhasil dihapus`,
            sessionId,
            details: {
                sessionFound,
                markedForDeletion: fs.existsSync(sessionPath)
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error(`❌ Delete session error: ${error.message}`);
        
        res.json({
            success: true,
            message: `Session ${sessionId} dihapus (dengan warning)`,
            warning: error.message,
            sessionId,
            timestamp: new Date().toISOString()
        });
    }
});

// Helper function untuk delete session folder dengan retry
async function deleteSessionFolder(folderPath) {
    const maxRetries = 3;
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
        try {
            if (!fs.existsSync(folderPath)) {
                console.log(`📁 Folder already deleted: ${folderPath}`);
                return;
            }
            
            // Try different methods
            if (process.platform === 'win32') {
                // Windows - use rmdir command
                await new Promise((resolve, reject) => {
                    const rmdir = spawn('cmd', ['/c', 'rmdir', '/s', '/q', `"${folderPath}"`], { 
                        stdio: 'pipe'
                    });
                    
                    rmdir.on('close', (code) => {
                        if (code === 0) {
                            resolve();
                        } else {
                            reject(new Error(`rmdir failed with code ${code}`));
                        }
                    });
                    
                    rmdir.on('error', reject);
                });
            } else {
                // Linux/Mac
                fs.rmSync(folderPath, { recursive: true, force: true });
            }
            
            // Verify deletion
            if (!fs.existsSync(folderPath)) {
                console.log(`✅ Folder successfully deleted: ${folderPath}`);
                return;
            } else {
                throw new Error('Folder still exists after deletion attempt');
            }
            
        } catch (error) {
            retryCount++;
            console.log(`⚠️ Delete attempt ${retryCount} failed: ${error.message}`);
            
            if (retryCount < maxRetries) {
                console.log(`🔄 Retrying in 2 seconds...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                console.log(`❌ All delete attempts failed for: ${folderPath}`);
                throw error;
            }
        }
    }
}

// Function untuk sanitize sessionId
function sanitizeSessionId(sessionId) {
    // Hanya izinkan alphanumeric, underscore, dan hyphen
    return sessionId.replace(/[^a-zA-Z0-9_-]/g, '');
}

// Manual recursive delete function
async function deleteRecursive(dirPath) {
    if (!fs.existsSync(dirPath)) return;
    
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            await deleteRecursive(filePath);
        } else {
            try {
                fs.unlinkSync(filePath);
            } catch (unlinkError) {
                console.log(`⚠️ Failed to delete file ${filePath}: ${unlinkError.message}`);
            }
        }
    }
    
    try {
        fs.rmdirSync(dirPath);
    } catch (rmdirError) {
        console.log(`⚠️ Failed to remove directory ${dirPath}: ${rmdirError.message}`);
    }
}

// Redirect ke halaman session jika ada parameter create
app.get('/', (req, res, next) => {
    if (req.query.create) {
        const sessionId = req.query.create.replace(/[^a-zA-Z0-9_-]/g, ''); // Sanitize
        if (sessionId) {
            return res.redirect(`/session/${sessionId}`);
        }
    }
    next();
});

// Cleanup sessions yang sudah lama tidak aktif (optional)
setInterval(() => {
    sessions.forEach(async (session, sessionId) => {
        if (session.status === 'disconnected') {
            console.log(`🧹 Cleaning up disconnected session: ${sessionId}`);
            
            // Jangan langsung delete, beri waktu untuk browser close
            setTimeout(async () => {
                try {
                    if (sessions.has(sessionId) && sessions.get(sessionId).status === 'disconnected') {
                        await session.destroy();
                        sessions.delete(sessionId);
                        console.log(`✅ Delayed cleanup completed: ${sessionId}`);
                    }
                } catch (cleanupError) {
                    console.log(`⚠️ Cleanup error ignored: ${cleanupError.message}`);
                }
            }, 10000); // 10 detik delay
        }
    });
}, 300000); // 5 menit

// Error handler
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        availableEndpoints: {
            web: [
                'GET / - Dashboard utama',
                'GET /session/:sessionId - Management session',
                'POST /session/:sessionId/send - Kirim pesan via web form',
                'POST /session/:sessionId/logout - Logout session'
            ],
            api: [
                'GET /api/sessions - List semua sessions',
                'GET /api/:sessionId/status - Status session',
                'GET /api/:sessionId/qr - QR code untuk login',
                'POST /api/:sessionId/send - Kirim pesan via API'
            ]
        }
    });
});

// Perbaiki global error handler untuk ignore EBUSY errors
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error.message);
    if (error.message.includes('Execution context was destroyed') || 
        error.message.includes('Protocol error') ||
        error.message.includes('EBUSY') ||
        error.message.includes('resource busy')) {
        console.log('⚠️ Ignoring browser/file lock error');
        return; // Don't crash
    }
    // For other critical errors, you might want to restart
});

process.on('unhandledRejection', (reason, promise) => {
    if (reason && reason.message && 
        (reason.message.includes('Execution context was destroyed') || 
         reason.message.includes('Protocol error') ||
         reason.message.includes('EBUSY') ||
         reason.message.includes('resource busy'))) {
        console.log('⚠️ Ignoring browser/file lock rejection');
        return; // Don't crash
    }
    console.error('❌ Unhandled Rejection:', reason);
});

app.listen(PORT, () => {
    console.log(`🚀 Multi-Session WhatsApp Gateway berjalan di http://localhost:${PORT}`);
    console.log(`📱 Dashboard: http://localhost:${PORT}`);
    console.log(`📡 API Docs: http://localhost:${PORT}/api/sessions`);
    console.log(`🔧 Buat session baru: http://localhost:${PORT}/?create=project1`);
    
    // Restore sessions yang sudah ada
    console.log('\n🔄 Checking for existing sessions...');
    restoreExistingSessions();
});

// Tambahkan endpoint untuk force refresh sessions
app.post('/api/refresh-sessions', (req, res) => {
    console.log('🔄 Force refresh sessions requested');
    
    // Clear current sessions
    sessions.clear();
    
    // Restore from folders
    restoreExistingSessions();
    
    const sessionList = Array.from(sessions.keys());
    
    res.json({
        success: true,
        message: 'Sessions refreshed',
        totalSessions: sessionList.length,
        sessions: sessionList,
        timestamp: new Date().toISOString()
    });
});

// Tambahkan endpoint untuk debug folder structure
app.get('/api/debug/folders', (req, res) => {
    const authPath = path.join(__dirname, '.wwebjs_auth');
    
    try {
        if (!fs.existsSync(authPath)) {
            return res.json({
                success: true,
                message: 'Auth folder does not exist',
                folders: []
            });
        }
        
        const folders = fs.readdirSync(authPath);
        const folderDetails = [];
        
        folders.forEach(folder => {
            const folderPath = path.join(authPath, folder);
            const stats = fs.statSync(folderPath);
            
            folderDetails.push({
                name: folder,
                isDirectory: stats.isDirectory(),
                size: stats.size,
                modified: stats.mtime,
                isSessionFolder: folder.startsWith('session-'),
                sessionId: folder.startsWith('session-') ? folder.replace('session-', '') : null
            });
        });
        
        res.json({
            success: true,
            authPath,
            totalFolders: folders.length,
            folders: folderDetails,
            activeSessions: Array.from(sessions.keys()),
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            authPath
        });
    }
});

// Tambahkan endpoint untuk debug folder structure
app.get('/api/debug/folders', (req, res) => {
    const authPath = path.join(__dirname, '.wwebjs_auth');
    
    try {
        if (!fs.existsSync(authPath)) {
            return res.json({
                success: true,
                message: 'Auth folder does not exist',
                folders: []
            });
        }
        
        const folders = fs.readdirSync(authPath);
        const folderDetails = [];
        
        folders.forEach(folder => {
            const folderPath = path.join(authPath, folder);
            const stats = fs.statSync(folderPath);
            
            folderDetails.push({
                name: folder,
                isDirectory: stats.isDirectory(),
                size: stats.size,
                modified: stats.mtime,
                isSessionFolder: folder.startsWith('session-'),
                sessionId: folder.startsWith('session-') ? folder.replace('session-', '') : null
            });
        });
        
        res.json({
            success: true,
            authPath,
            totalFolders: folders.length,
            folders: folderDetails,
            activeSessions: Array.from(sessions.keys()),
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            authPath
        });
    }
});
